{"originHash": "35a4274de4a9344954340de86a77d37d31595b998aac6c36cf29a3d4f14b5557", "pins": [{"identity": "bettersafariview", "kind": "remoteSourceControl", "location": "https://github.com/stleamist/BetterSafariView", "state": {"revision": "884533749e55949b2566030948c6fdbe2873ed0c", "version": "2.4.2"}}, {"identity": "colorfulx", "kind": "remoteSourceControl", "location": "https://github.com/Lakr233/ColorfulX", "state": {"revision": "4b48da803e2e02bdd29c031802d995bf28eea486", "version": "5.7.0"}}, {"identity": "colorvector", "kind": "remoteSourceControl", "location": "https://github.com/Lakr233/ColorVector.git", "state": {"revision": "6da8726bf38d68eb943d0f2139ac2a1fac70e65b", "version": "1.0.4"}}, {"identity": "iqkeyboardmanager", "kind": "remoteSourceControl", "location": "https://github.com/hackiftekhar/IQKeyboardManager", "state": {"revision": "c00b1ae9fa1ad8af4465bb6ca901f6943fc98eba", "version": "6.5.16"}}, {"identity": "keyboardshortcuts", "kind": "remoteSourceControl", "location": "https://github.com/sindresorhus/KeyboardShortcuts", "state": {"revision": "045cf174010beb335fa1d2567d18c057b8787165", "version": "2.3.0"}}, {"identity": "msdisplaylink", "kind": "remoteSourceControl", "location": "https://github.com/Lakr233/MSDisplayLink.git", "state": {"revision": "ebf5823cb5fc1326639d9a05bc06d16bbe82989f", "version": "2.0.8"}}, {"identity": "networkimage", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/NetworkImage", "state": {"revision": "2849f5323265386e200484b0d0f896e73c3411b9", "version": "6.0.1"}}, {"identity": "neumorphic", "kind": "remoteSourceControl", "location": "https://github.com/gewill/neumorphic.git", "state": {"branch": "master", "revision": "3e54160a6a72596fb95b110fc160de5116429ec2"}}, {"identity": "purchases-ios-spm", "kind": "remoteSourceControl", "location": "https://github.com/RevenueCat/purchases-ios-spm.git", "state": {"revision": "709554156bb93c909b362e0b30ec0f2afee3df6e", "version": "5.33.1"}}, {"identity": "springinterpolation", "kind": "remoteSourceControl", "location": "https://github.com/Lakr233/SpringInterpolation.git", "state": {"revision": "f9ae95ece5d6b7cdceafd4381f1d5f0f9494e5d2", "version": "1.3.1"}}, {"identity": "swift-cmark", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-cmark", "state": {"revision": "b022b08312decdc46585e0b3440d97f6f22ef703", "version": "0.6.0"}}, {"identity": "swift-markdown-ui", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/swift-markdown-ui", "state": {"revision": "5f613358148239d0292c0cef674a3c2314737f9e", "version": "2.4.1"}}, {"identity": "swiftui-introspect", "kind": "remoteSourceControl", "location": "https://github.com/siteline/swiftui-introspect", "state": {"revision": "807f73ce09a9b9723f12385e592b4e0aaebd3336", "version": "1.3.0"}}, {"identity": "swiftuioverlaycontainer", "kind": "remoteSourceControl", "location": "https://github.com/fatbobman/SwiftUIOverlayContainer", "state": {"revision": "64a124ba613adc9f2bb7bf00f5a5e8b22a816d14", "version": "2.4.1"}}, {"identity": "swiftuirouter", "kind": "remoteSourceControl", "location": "https://github.com/frzi/SwiftUIRouter.git", "state": {"revision": "e0cb32b54bb429aa43ce30240b0036bf223a62a3", "version": "1.4.0"}}, {"identity": "swiftyopencc", "kind": "remoteSourceControl", "location": "https://github.com/gewill/SwiftyOpenCC", "state": {"branch": "master", "revision": "c11cb6431ad17187111d80b0f3daf6485f8a7e6c"}}, {"identity": "swiftyuserdefaults", "kind": "remoteSourceControl", "location": "https://github.com/sunshinejr/SwiftyUserDefaults", "state": {"revision": "f66bcd04088582c8fbb5cb8554d577e303bae396", "version": "5.3.0"}}, {"identity": "visualeffects", "kind": "remoteSourceControl", "location": "https://github.com/twostraws/VisualEffects", "state": {"revision": "8ff7f90f9650d9db5774275d0b1f07ef642ecf4d", "version": "1.0.3"}}], "version": 3}