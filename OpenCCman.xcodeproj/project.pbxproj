// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		09103B652E40558F00802E45 /* Help-zh-Hans.md in Resources */ = {isa = PBXBuildFile; fileRef = 09103B622E40558F00802E45 /* Help-zh-Hans.md */; };
		09103B662E40558F00802E45 /* Help-zh-Hant.md in Resources */ = {isa = PBXBuildFile; fileRef = 09103B632E40558F00802E45 /* Help-zh-Hant.md */; };
		09103B672E40558F00802E45 /* Help.md in Resources */ = {isa = PBXBuildFile; fileRef = 09103B612E40558F00802E45 /* Help.md */; };
		09103B6A2E40563800802E45 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 09103B692E40563800802E45 /* MarkdownUI */; };
		0920E96C2E3F4D3C003C9715 /* InfoPlist.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 0920E96B2E3F4D3C003C9715 /* InfoPlist.xcstrings */; };
		09DBEC4F2E3BC32F00DD9C5B /* HotkeySettingsScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 09DBEC4E2E3BC32F00DD9C5B /* HotkeySettingsScene.swift */; };
		09DBEC522E3BC4BC00DD9C5B /* KeyboardShortcuts in Frameworks */ = {isa = PBXBuildFile; productRef = 09DBEC512E3BC4BC00DD9C5B /* KeyboardShortcuts */; };
		09DBEC552E3BC69000DD9C5B /* GlobalShortcutService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 09DBEC532E3BC69000DD9C5B /* GlobalShortcutService.swift */; };
		09FB99902E3D060700DBDB5D /* SwiftUIOverlayContainer in Frameworks */ = {isa = PBXBuildFile; productRef = 09FB998F2E3D060700DBDB5D /* SwiftUIOverlayContainer */; };
		BE2C6D3E2B2D7EA700FC4112 /* SwiftUIIntrospect in Frameworks */ = {isa = PBXBuildFile; productRef = BE2C6D3D2B2D7EA700FC4112 /* SwiftUIIntrospect */; };
		BE2C6D502B2D7ED000FC4112 /* ViewExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D402B2D7ED000FC4112 /* ViewExtensions.swift */; };
		BE2C6D512B2D7ED000FC4112 /* SKProductExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D412B2D7ED000FC4112 /* SKProductExtensions.swift */; };
		BE2C6D522B2D7ED000FC4112 /* ImageExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D422B2D7ED000FC4112 /* ImageExtensions.swift */; };
		BE2C6D542B2D7ED000FC4112 /* StringExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D442B2D7ED000FC4112 /* StringExtensions.swift */; };
		BE2C6D552B2D7ED000FC4112 /* ColorExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D452B2D7ED000FC4112 /* ColorExtensions.swift */; };
		BE2C6D562B2D7ED000FC4112 /* BoolExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D462B2D7ED000FC4112 /* BoolExtensions.swift */; };
		BE2C6D572B2D7ED000FC4112 /* DoubleExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D472B2D7ED000FC4112 /* DoubleExtensions.swift */; };
		BE2C6D592B2D7ED000FC4112 /* BundleExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D492B2D7ED000FC4112 /* BundleExtensions.swift */; };
		BE2C6D5A2B2D7ED000FC4112 /* IntExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D4A2B2D7ED000FC4112 /* IntExtensions.swift */; };
		BE2C6D5C2B2D7ED000FC4112 /* DateExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D4C2B2D7ED000FC4112 /* DateExtensions.swift */; };
		BE2C6D632B2D80D100FC4112 /* Styles.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D612B2D80D100FC4112 /* Styles.swift */; };
		BE2C6D642B2D80D100FC4112 /* ButtonStyles.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D622B2D80D100FC4112 /* ButtonStyles.swift */; };
		BE2C6D672B2D817100FC4112 /* Neumorphic in Frameworks */ = {isa = PBXBuildFile; productRef = BE2C6D662B2D817100FC4112 /* Neumorphic */; };
		BE2C6D692B2D81A300FC4112 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2C6D682B2D81A300FC4112 /* Constants.swift */; };
		BE3AEC892B2EF17700820673 /* RootView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AEC882B2EF17700820673 /* RootView.swift */; };
		BE3AEC8D2B2EF1DF00820673 /* SwiftUIRouter in Frameworks */ = {isa = PBXBuildFile; productRef = BE3AEC8C2B2EF1DF00820673 /* SwiftUIRouter */; };
		BE3AEC942B2EF25000820673 /* ProScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AEC8E2B2EF25000820673 /* ProScene.swift */; };
		BE3AEC952B2EF25000820673 /* SettingsScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AEC8F2B2EF25000820673 /* SettingsScene.swift */; };
		BE3AEC962B2EF25000820673 /* HelpScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AEC902B2EF25000820673 /* HelpScene.swift */; };
		BE3AEC972B2EF25000820673 /* ChangeLanguageScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AEC912B2EF25000820673 /* ChangeLanguageScene.swift */; };
		BE3AEC982B2EF25000820673 /* FeedbackScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AEC922B2EF25000820673 /* FeedbackScene.swift */; };
		BE3AEC992B2EF25000820673 /* OpenSourceScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AEC932B2EF25000820673 /* OpenSourceScene.swift */; };
		BE3AEC9E2B2EF2D400820673 /* IAPManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AEC9D2B2EF2D400820673 /* IAPManager.swift */; };
		BE3AECA12B2EF36500820673 /* BetterSafariView in Frameworks */ = {isa = PBXBuildFile; productRef = BE3AECA02B2EF36500820673 /* BetterSafariView */; };
		BE3AECA42B2EF3B200820673 /* BackButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECA22B2EF3B200820673 /* BackButton.swift */; };
		BE3AECA52B2EF3B200820673 /* CellButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECA32B2EF3B200820673 /* CellButton.swift */; };
		BE3AECA72B2EF43C00820673 /* MyAppModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECA62B2EF43C00820673 /* MyAppModel.swift */; };
		BE3AECA92B2EF46000820673 /* OpenSourceModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECA82B2EF46000820673 /* OpenSourceModel.swift */; };
		BE3AECAB2B2EF46A00820673 /* TestNumbersPerDayManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECAA2B2EF46A00820673 /* TestNumbersPerDayManager.swift */; };
		BE3AECAE2B2EF4FA00820673 /* SwiftyUserDefaults in Frameworks */ = {isa = PBXBuildFile; productRef = BE3AECAD2B2EF4FA00820673 /* SwiftyUserDefaults */; };
		BE3AECB42B2EF6F700820673 /* AlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECAF2B2EF6F700820673 /* AlertView.swift */; };
		BE3AECB52B2EF6F700820673 /* CardReflectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECB02B2EF6F700820673 /* CardReflectionView.swift */; };
		BE3AECB62B2EF6F700820673 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECB12B2EF6F700820673 /* LoadingView.swift */; };
		BE3AECB72B2EF6F700820673 /* MyAppView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECB22B2EF6F700820673 /* MyAppView.swift */; };
		BE3AECB82B2EF6F700820673 /* ProAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3AECB32B2EF6F700820673 /* ProAlertView.swift */; };
		BE3F71DF2B2F4F47001496EA /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3F71DE2B2F4F47001496EA /* AppDelegate.swift */; };
		BE3F71E22B2F4F8F001496EA /* IQKeyboardManagerSwift in Frameworks */ = {isa = PBXBuildFile; platformFilter = ios; productRef = BE3F71E12B2F4F8F001496EA /* IQKeyboardManagerSwift */; };
		BE3F71E42B2F5377001496EA /* UIApplicationExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3F71E32B2F5377001496EA /* UIApplicationExtensions.swift */; };
		BE4296832B41BB7300AA1139 /* ReviewHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE4296812B41BB7300AA1139 /* ReviewHandler.swift */; };
		BE4296862B41BF3100AA1139 /* PickableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE4296852B41BF3100AA1139 /* PickableView.swift */; };
		BE727E2C2B2DE16400C63DB2 /* HomeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE727E2B2B2DE16400C63DB2 /* HomeViewModel.swift */; };
		BE727E2E2B2DF60400C63DB2 /* SegmentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE727E2D2B2DF60400C63DB2 /* SegmentView.swift */; };
		BE727E322B2DF65D00C63DB2 /* VisualEffects in Frameworks */ = {isa = PBXBuildFile; productRef = BE727E312B2DF65D00C63DB2 /* VisualEffects */; };
		BE727E342B2DF85E00C63DB2 /* UserDefaultsKeys.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE727E332B2DF85E00C63DB2 /* UserDefaultsKeys.swift */; };
		BE727E382B2E00D000C63DB2 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = BE727E3A2B2E00D000C63DB2 /* Localizable.strings */; };
		BE781CB22CA131790022C4B8 /* RevenueCat in Frameworks */ = {isa = PBXBuildFile; productRef = BE781CB12CA131790022C4B8 /* RevenueCat */; };
		BE781CB42CA131790022C4B8 /* RevenueCatUI in Frameworks */ = {isa = PBXBuildFile; productRef = BE781CB32CA131790022C4B8 /* RevenueCatUI */; };
		BEA770E72B2C904900AFA2F0 /* OpenCCmanApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA770E62B2C904900AFA2F0 /* OpenCCmanApp.swift */; };
		BEA770E92B2C904900AFA2F0 /* HomeScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA770E82B2C904900AFA2F0 /* HomeScene.swift */; };
		BEA770EB2B2C904A00AFA2F0 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = BEA770EA2B2C904A00AFA2F0 /* Assets.xcassets */; };
		BEA770EF2B2C904A00AFA2F0 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = BEA770EE2B2C904A00AFA2F0 /* Preview Assets.xcassets */; };
		BEA770F72B2C92B700AFA2F0 /* OpenCC in Frameworks */ = {isa = PBXBuildFile; productRef = BEA770F62B2C92B700AFA2F0 /* OpenCC */; };
		BEA956392B31EB7B00E04625 /* ColorfulX in Frameworks */ = {isa = PBXBuildFile; productRef = BEA956382B31EB7B00E04625 /* ColorfulX */; };
		BEA9563D2B31FF8C00E04625 /* ChangeColorSchemeScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA9563C2B31FF8C00E04625 /* ChangeColorSchemeScene.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		09103B612E40558F00802E45 /* Help.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = Help.md; sourceTree = "<group>"; };
		09103B622E40558F00802E45 /* Help-zh-Hans.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "Help-zh-Hans.md"; sourceTree = "<group>"; };
		09103B632E40558F00802E45 /* Help-zh-Hant.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "Help-zh-Hant.md"; sourceTree = "<group>"; };
		0920E96B2E3F4D3C003C9715 /* InfoPlist.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = InfoPlist.xcstrings; sourceTree = "<group>"; };
		09DBEC4E2E3BC32F00DD9C5B /* HotkeySettingsScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HotkeySettingsScene.swift; sourceTree = "<group>"; };
		09DBEC532E3BC69000DD9C5B /* GlobalShortcutService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GlobalShortcutService.swift; sourceTree = "<group>"; };
		BE2C6D402B2D7ED000FC4112 /* ViewExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ViewExtensions.swift; sourceTree = "<group>"; };
		BE2C6D412B2D7ED000FC4112 /* SKProductExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SKProductExtensions.swift; sourceTree = "<group>"; };
		BE2C6D422B2D7ED000FC4112 /* ImageExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ImageExtensions.swift; sourceTree = "<group>"; };
		BE2C6D442B2D7ED000FC4112 /* StringExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StringExtensions.swift; sourceTree = "<group>"; };
		BE2C6D452B2D7ED000FC4112 /* ColorExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ColorExtensions.swift; sourceTree = "<group>"; };
		BE2C6D462B2D7ED000FC4112 /* BoolExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BoolExtensions.swift; sourceTree = "<group>"; };
		BE2C6D472B2D7ED000FC4112 /* DoubleExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DoubleExtensions.swift; sourceTree = "<group>"; };
		BE2C6D492B2D7ED000FC4112 /* BundleExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BundleExtensions.swift; sourceTree = "<group>"; };
		BE2C6D4A2B2D7ED000FC4112 /* IntExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IntExtensions.swift; sourceTree = "<group>"; };
		BE2C6D4C2B2D7ED000FC4112 /* DateExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DateExtensions.swift; sourceTree = "<group>"; };
		BE2C6D612B2D80D100FC4112 /* Styles.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Styles.swift; sourceTree = "<group>"; };
		BE2C6D622B2D80D100FC4112 /* ButtonStyles.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ButtonStyles.swift; sourceTree = "<group>"; };
		BE2C6D682B2D81A300FC4112 /* Constants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		BE3AEC882B2EF17700820673 /* RootView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RootView.swift; sourceTree = "<group>"; };
		BE3AEC8E2B2EF25000820673 /* ProScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProScene.swift; sourceTree = "<group>"; };
		BE3AEC8F2B2EF25000820673 /* SettingsScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingsScene.swift; sourceTree = "<group>"; };
		BE3AEC902B2EF25000820673 /* HelpScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HelpScene.swift; sourceTree = "<group>"; };
		BE3AEC912B2EF25000820673 /* ChangeLanguageScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangeLanguageScene.swift; sourceTree = "<group>"; };
		BE3AEC922B2EF25000820673 /* FeedbackScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FeedbackScene.swift; sourceTree = "<group>"; };
		BE3AEC932B2EF25000820673 /* OpenSourceScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OpenSourceScene.swift; sourceTree = "<group>"; };
		BE3AEC9D2B2EF2D400820673 /* IAPManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IAPManager.swift; sourceTree = "<group>"; };
		BE3AECA22B2EF3B200820673 /* BackButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BackButton.swift; sourceTree = "<group>"; };
		BE3AECA32B2EF3B200820673 /* CellButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CellButton.swift; sourceTree = "<group>"; };
		BE3AECA62B2EF43C00820673 /* MyAppModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyAppModel.swift; sourceTree = "<group>"; };
		BE3AECA82B2EF46000820673 /* OpenSourceModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OpenSourceModel.swift; sourceTree = "<group>"; };
		BE3AECAA2B2EF46A00820673 /* TestNumbersPerDayManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TestNumbersPerDayManager.swift; sourceTree = "<group>"; };
		BE3AECAF2B2EF6F700820673 /* AlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AlertView.swift; sourceTree = "<group>"; };
		BE3AECB02B2EF6F700820673 /* CardReflectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CardReflectionView.swift; sourceTree = "<group>"; };
		BE3AECB12B2EF6F700820673 /* LoadingView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		BE3AECB22B2EF6F700820673 /* MyAppView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyAppView.swift; sourceTree = "<group>"; };
		BE3AECB32B2EF6F700820673 /* ProAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProAlertView.swift; sourceTree = "<group>"; };
		BE3F71DE2B2F4F47001496EA /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		BE3F71E32B2F5377001496EA /* UIApplicationExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIApplicationExtensions.swift; sourceTree = "<group>"; };
		BE4296812B41BB7300AA1139 /* ReviewHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReviewHandler.swift; sourceTree = "<group>"; };
		BE4296852B41BF3100AA1139 /* PickableView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PickableView.swift; sourceTree = "<group>"; };
		BE727E2B2B2DE16400C63DB2 /* HomeViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeViewModel.swift; sourceTree = "<group>"; };
		BE727E2D2B2DF60400C63DB2 /* SegmentView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SegmentView.swift; sourceTree = "<group>"; };
		BE727E332B2DF85E00C63DB2 /* UserDefaultsKeys.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserDefaultsKeys.swift; sourceTree = "<group>"; };
		BE727E392B2E00D000C63DB2 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		BE727E3B2B2E00D300C63DB2 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		BE727E3C2B2E00D500C63DB2 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		BEA770E32B2C904900AFA2F0 /* OpenCCman.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = OpenCCman.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEA770E62B2C904900AFA2F0 /* OpenCCmanApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpenCCmanApp.swift; sourceTree = "<group>"; };
		BEA770E82B2C904900AFA2F0 /* HomeScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeScene.swift; sourceTree = "<group>"; };
		BEA770EA2B2C904A00AFA2F0 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		BEA770EC2B2C904A00AFA2F0 /* OpenCCman.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = OpenCCman.entitlements; sourceTree = "<group>"; };
		BEA770EE2B2C904A00AFA2F0 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		BEA770F82B2CAEA900AFA2F0 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		BEA9563C2B31FF8C00E04625 /* ChangeColorSchemeScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangeColorSchemeScene.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		BEA770E02B2C904900AFA2F0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEA956392B31EB7B00E04625 /* ColorfulX in Frameworks */,
				09103B6A2E40563800802E45 /* MarkdownUI in Frameworks */,
				BEA770F72B2C92B700AFA2F0 /* OpenCC in Frameworks */,
				BE3AEC8D2B2EF1DF00820673 /* SwiftUIRouter in Frameworks */,
				BE781CB22CA131790022C4B8 /* RevenueCat in Frameworks */,
				BE727E322B2DF65D00C63DB2 /* VisualEffects in Frameworks */,
				09FB99902E3D060700DBDB5D /* SwiftUIOverlayContainer in Frameworks */,
				09DBEC522E3BC4BC00DD9C5B /* KeyboardShortcuts in Frameworks */,
				BE2C6D3E2B2D7EA700FC4112 /* SwiftUIIntrospect in Frameworks */,
				BE3F71E22B2F4F8F001496EA /* IQKeyboardManagerSwift in Frameworks */,
				BE781CB42CA131790022C4B8 /* RevenueCatUI in Frameworks */,
				BE3AECA12B2EF36500820673 /* BetterSafariView in Frameworks */,
				BE2C6D672B2D817100FC4112 /* Neumorphic in Frameworks */,
				BE3AECAE2B2EF4FA00820673 /* SwiftyUserDefaults in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		09103B642E40558F00802E45 /* Resources */ = {
			isa = PBXGroup;
			children = (
				09103B612E40558F00802E45 /* Help.md */,
				09103B622E40558F00802E45 /* Help-zh-Hans.md */,
				09103B632E40558F00802E45 /* Help-zh-Hant.md */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		09DBEC542E3BC69000DD9C5B /* Services */ = {
			isa = PBXGroup;
			children = (
				09DBEC532E3BC69000DD9C5B /* GlobalShortcutService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		BE2C6D3F2B2D7ED000FC4112 /* extensions */ = {
			isa = PBXGroup;
			children = (
				BE2C6D402B2D7ED000FC4112 /* ViewExtensions.swift */,
				BE2C6D412B2D7ED000FC4112 /* SKProductExtensions.swift */,
				BE2C6D422B2D7ED000FC4112 /* ImageExtensions.swift */,
				BE2C6D442B2D7ED000FC4112 /* StringExtensions.swift */,
				BE2C6D452B2D7ED000FC4112 /* ColorExtensions.swift */,
				BE2C6D462B2D7ED000FC4112 /* BoolExtensions.swift */,
				BE2C6D472B2D7ED000FC4112 /* DoubleExtensions.swift */,
				BE2C6D492B2D7ED000FC4112 /* BundleExtensions.swift */,
				BE2C6D4A2B2D7ED000FC4112 /* IntExtensions.swift */,
				BE2C6D4C2B2D7ED000FC4112 /* DateExtensions.swift */,
				BE3F71E32B2F5377001496EA /* UIApplicationExtensions.swift */,
			);
			path = extensions;
			sourceTree = "<group>";
		};
		BE2C6D602B2D80D100FC4112 /* Styles */ = {
			isa = PBXGroup;
			children = (
				BE2C6D612B2D80D100FC4112 /* Styles.swift */,
				BE2C6D622B2D80D100FC4112 /* ButtonStyles.swift */,
			);
			path = Styles;
			sourceTree = "<group>";
		};
		BE3AEC8A2B2EF18F00820673 /* Scene */ = {
			isa = PBXGroup;
			children = (
				BE3AEC882B2EF17700820673 /* RootView.swift */,
				BEA770E82B2C904900AFA2F0 /* HomeScene.swift */,
				BE727E2B2B2DE16400C63DB2 /* HomeViewModel.swift */,
				BE3AEC8F2B2EF25000820673 /* SettingsScene.swift */,
				09DBEC4E2E3BC32F00DD9C5B /* HotkeySettingsScene.swift */,
				BE3AEC922B2EF25000820673 /* FeedbackScene.swift */,
				BE3AEC902B2EF25000820673 /* HelpScene.swift */,
				BE3AEC932B2EF25000820673 /* OpenSourceScene.swift */,
				BE3AEC8E2B2EF25000820673 /* ProScene.swift */,
				BE3AEC912B2EF25000820673 /* ChangeLanguageScene.swift */,
				BEA9563C2B31FF8C00E04625 /* ChangeColorSchemeScene.swift */,
			);
			path = Scene;
			sourceTree = "<group>";
		};
		BE4296802B41BB7300AA1139 /* Helpers */ = {
			isa = PBXGroup;
			children = (
				BE4296812B41BB7300AA1139 /* ReviewHandler.swift */,
			);
			path = Helpers;
			sourceTree = "<group>";
		};
		BE727E2F2B2DF60E00C63DB2 /* View */ = {
			isa = PBXGroup;
			children = (
				BE3AECA22B2EF3B200820673 /* BackButton.swift */,
				BE3AECA32B2EF3B200820673 /* CellButton.swift */,
				BE3AECAF2B2EF6F700820673 /* AlertView.swift */,
				BE3AECB02B2EF6F700820673 /* CardReflectionView.swift */,
				BE3AECB12B2EF6F700820673 /* LoadingView.swift */,
				BE3AECB22B2EF6F700820673 /* MyAppView.swift */,
				BE3AECB32B2EF6F700820673 /* ProAlertView.swift */,
				BE727E2D2B2DF60400C63DB2 /* SegmentView.swift */,
				BE4296852B41BF3100AA1139 /* PickableView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		BE727E352B2DF86500C63DB2 /* Model */ = {
			isa = PBXGroup;
			children = (
				BE727E332B2DF85E00C63DB2 /* UserDefaultsKeys.swift */,
				BE3AECA62B2EF43C00820673 /* MyAppModel.swift */,
				BE3AECA82B2EF46000820673 /* OpenSourceModel.swift */,
				BE3AECAA2B2EF46A00820673 /* TestNumbersPerDayManager.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		BEA770DA2B2C904900AFA2F0 = {
			isa = PBXGroup;
			children = (
				BEA770E52B2C904900AFA2F0 /* OpenCCman */,
				BEA770E42B2C904900AFA2F0 /* Products */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
		};
		BEA770E42B2C904900AFA2F0 /* Products */ = {
			isa = PBXGroup;
			children = (
				BEA770E32B2C904900AFA2F0 /* OpenCCman.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BEA770E52B2C904900AFA2F0 /* OpenCCman */ = {
			isa = PBXGroup;
			children = (
				BEA770F82B2CAEA900AFA2F0 /* Info.plist */,
				BEA770E62B2C904900AFA2F0 /* OpenCCmanApp.swift */,
				BE3F71DE2B2F4F47001496EA /* AppDelegate.swift */,
				BE2C6D682B2D81A300FC4112 /* Constants.swift */,
				BE3AEC9D2B2EF2D400820673 /* IAPManager.swift */,
				BEA770EA2B2C904A00AFA2F0 /* Assets.xcassets */,
				BEA770EC2B2C904A00AFA2F0 /* OpenCCman.entitlements */,
				0920E96B2E3F4D3C003C9715 /* InfoPlist.xcstrings */,
				BE727E3A2B2E00D000C63DB2 /* Localizable.strings */,
				09DBEC542E3BC69000DD9C5B /* Services */,
				BE3AEC8A2B2EF18F00820673 /* Scene */,
				BE727E2F2B2DF60E00C63DB2 /* View */,
				BE727E352B2DF86500C63DB2 /* Model */,
				BE2C6D602B2D80D100FC4112 /* Styles */,
				BE4296802B41BB7300AA1139 /* Helpers */,
				BE2C6D3F2B2D7ED000FC4112 /* extensions */,
				09103B642E40558F00802E45 /* Resources */,
				BEA770ED2B2C904A00AFA2F0 /* Preview Content */,
			);
			path = OpenCCman;
			sourceTree = "<group>";
		};
		BEA770ED2B2C904A00AFA2F0 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				BEA770EE2B2C904A00AFA2F0 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		BEA770E22B2C904900AFA2F0 /* OpenCCman */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BEA770F22B2C904A00AFA2F0 /* Build configuration list for PBXNativeTarget "OpenCCman" */;
			buildPhases = (
				BEA770DF2B2C904900AFA2F0 /* Sources */,
				BEA770E02B2C904900AFA2F0 /* Frameworks */,
				BEA770E12B2C904900AFA2F0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = OpenCCman;
			packageProductDependencies = (
				BEA770F62B2C92B700AFA2F0 /* OpenCC */,
				BE2C6D3D2B2D7EA700FC4112 /* SwiftUIIntrospect */,
				BE2C6D662B2D817100FC4112 /* Neumorphic */,
				BE727E312B2DF65D00C63DB2 /* VisualEffects */,
				BE3AEC8C2B2EF1DF00820673 /* SwiftUIRouter */,
				BE3AECA02B2EF36500820673 /* BetterSafariView */,
				BE3AECAD2B2EF4FA00820673 /* SwiftyUserDefaults */,
				BE3F71E12B2F4F8F001496EA /* IQKeyboardManagerSwift */,
				BEA956382B31EB7B00E04625 /* ColorfulX */,
				BE781CB12CA131790022C4B8 /* RevenueCat */,
				BE781CB32CA131790022C4B8 /* RevenueCatUI */,
				09DBEC512E3BC4BC00DD9C5B /* KeyboardShortcuts */,
				09FB998F2E3D060700DBDB5D /* SwiftUIOverlayContainer */,
				09103B692E40563800802E45 /* MarkdownUI */,
			);
			productName = OpenCCman;
			productReference = BEA770E32B2C904900AFA2F0 /* OpenCCman.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BEA770DB2B2C904900AFA2F0 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1510;
				LastUpgradeCheck = 1510;
				TargetAttributes = {
					BEA770E22B2C904900AFA2F0 = {
						CreatedOnToolsVersion = 15.1;
					};
				};
			};
			buildConfigurationList = BEA770DE2B2C904900AFA2F0 /* Build configuration list for PBXProject "OpenCCman" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				"zh-Hant",
			);
			mainGroup = BEA770DA2B2C904900AFA2F0;
			packageReferences = (
				BEA770F52B2C92B700AFA2F0 /* XCRemoteSwiftPackageReference "SwiftyOpenCC" */,
				BE2C6D3C2B2D7EA700FC4112 /* XCRemoteSwiftPackageReference "swiftui-introspect" */,
				BE2C6D652B2D817100FC4112 /* XCRemoteSwiftPackageReference "neumorphic" */,
				BE727E302B2DF65D00C63DB2 /* XCRemoteSwiftPackageReference "VisualEffects" */,
				BE3AEC8B2B2EF1DF00820673 /* XCRemoteSwiftPackageReference "SwiftUIRouter" */,
				BE3AEC9F2B2EF36500820673 /* XCRemoteSwiftPackageReference "BetterSafariView" */,
				BE3AECAC2B2EF4FA00820673 /* XCRemoteSwiftPackageReference "SwiftyUserDefaults" */,
				BE3F71E02B2F4F8F001496EA /* XCRemoteSwiftPackageReference "IQKeyboardManager" */,
				BEA956372B31EB7B00E04625 /* XCRemoteSwiftPackageReference "ColorfulX" */,
				BE781CB02CA131790022C4B8 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */,
				09DBEC502E3BC4BC00DD9C5B /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */,
				09FB998E2E3D060700DBDB5D /* XCRemoteSwiftPackageReference "SwiftUIOverlayContainer" */,
				09103B682E40563800802E45 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */,
			);
			productRefGroup = BEA770E42B2C904900AFA2F0 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				BEA770E22B2C904900AFA2F0 /* OpenCCman */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		BEA770E12B2C904900AFA2F0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEA770EF2B2C904A00AFA2F0 /* Preview Assets.xcassets in Resources */,
				BE727E382B2E00D000C63DB2 /* Localizable.strings in Resources */,
				09103B652E40558F00802E45 /* Help-zh-Hans.md in Resources */,
				09103B662E40558F00802E45 /* Help-zh-Hant.md in Resources */,
				09103B672E40558F00802E45 /* Help.md in Resources */,
				0920E96C2E3F4D3C003C9715 /* InfoPlist.xcstrings in Resources */,
				BEA770EB2B2C904A00AFA2F0 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		BEA770DF2B2C904900AFA2F0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BE2C6D522B2D7ED000FC4112 /* ImageExtensions.swift in Sources */,
				BEA770E92B2C904900AFA2F0 /* HomeScene.swift in Sources */,
				BE3AECB52B2EF6F700820673 /* CardReflectionView.swift in Sources */,
				BE2C6D632B2D80D100FC4112 /* Styles.swift in Sources */,
				BE2C6D572B2D7ED000FC4112 /* DoubleExtensions.swift in Sources */,
				BE3AECB82B2EF6F700820673 /* ProAlertView.swift in Sources */,
				09DBEC552E3BC69000DD9C5B /* GlobalShortcutService.swift in Sources */,
				BE727E342B2DF85E00C63DB2 /* UserDefaultsKeys.swift in Sources */,
				BE2C6D542B2D7ED000FC4112 /* StringExtensions.swift in Sources */,
				BE3AEC892B2EF17700820673 /* RootView.swift in Sources */,
				BE2C6D692B2D81A300FC4112 /* Constants.swift in Sources */,
				BE3AEC942B2EF25000820673 /* ProScene.swift in Sources */,
				BE727E2C2B2DE16400C63DB2 /* HomeViewModel.swift in Sources */,
				BE2C6D502B2D7ED000FC4112 /* ViewExtensions.swift in Sources */,
				BE3F71DF2B2F4F47001496EA /* AppDelegate.swift in Sources */,
				BE2C6D5A2B2D7ED000FC4112 /* IntExtensions.swift in Sources */,
				BEA770E72B2C904900AFA2F0 /* OpenCCmanApp.swift in Sources */,
				BE3AEC992B2EF25000820673 /* OpenSourceScene.swift in Sources */,
				BE2C6D562B2D7ED000FC4112 /* BoolExtensions.swift in Sources */,
				BE727E2E2B2DF60400C63DB2 /* SegmentView.swift in Sources */,
				BE4296862B41BF3100AA1139 /* PickableView.swift in Sources */,
				BEA9563D2B31FF8C00E04625 /* ChangeColorSchemeScene.swift in Sources */,
				BE3AEC952B2EF25000820673 /* SettingsScene.swift in Sources */,
				BE2C6D512B2D7ED000FC4112 /* SKProductExtensions.swift in Sources */,
				09DBEC4F2E3BC32F00DD9C5B /* HotkeySettingsScene.swift in Sources */,
				BE3AEC982B2EF25000820673 /* FeedbackScene.swift in Sources */,
				BE3AECA52B2EF3B200820673 /* CellButton.swift in Sources */,
				BE3AECB62B2EF6F700820673 /* LoadingView.swift in Sources */,
				BE3AECA92B2EF46000820673 /* OpenSourceModel.swift in Sources */,
				BE3AECA72B2EF43C00820673 /* MyAppModel.swift in Sources */,
				BE3AEC972B2EF25000820673 /* ChangeLanguageScene.swift in Sources */,
				BE3AEC9E2B2EF2D400820673 /* IAPManager.swift in Sources */,
				BE2C6D5C2B2D7ED000FC4112 /* DateExtensions.swift in Sources */,
				BE2C6D642B2D80D100FC4112 /* ButtonStyles.swift in Sources */,
				BE3AECAB2B2EF46A00820673 /* TestNumbersPerDayManager.swift in Sources */,
				BE3AECA42B2EF3B200820673 /* BackButton.swift in Sources */,
				BE3F71E42B2F5377001496EA /* UIApplicationExtensions.swift in Sources */,
				BE2C6D552B2D7ED000FC4112 /* ColorExtensions.swift in Sources */,
				BE3AECB42B2EF6F700820673 /* AlertView.swift in Sources */,
				BE2C6D592B2D7ED000FC4112 /* BundleExtensions.swift in Sources */,
				BE3AEC962B2EF25000820673 /* HelpScene.swift in Sources */,
				BE4296832B41BB7300AA1139 /* ReviewHandler.swift in Sources */,
				BE3AECB72B2EF6F700820673 /* MyAppView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		BE727E3A2B2E00D000C63DB2 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				BE727E392B2E00D000C63DB2 /* en */,
				BE727E3B2B2E00D300C63DB2 /* zh-Hans */,
				BE727E3C2B2E00D500C63DB2 /* zh-Hant */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		BEA770F02B2C904A00AFA2F0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		BEA770F12B2C904A00AFA2F0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		BEA770F32B2C904A00AFA2F0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = OpenCCman/OpenCCman.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"OpenCCman/Preview Content\"";
				DEVELOPMENT_TEAM = RLK76T8Y89;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = OpenCCman/Info.plist;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSAppleEventsUsageDescription = "OpenCCman requires permission to execute AppleScript.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.1;
				PRODUCT_BUNDLE_IDENTIFIER = org.gewill.OpenCCman;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		BEA770F42B2C904A00AFA2F0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = OpenCCman/OpenCCman.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"OpenCCman/Preview Content\"";
				DEVELOPMENT_TEAM = RLK76T8Y89;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = OpenCCman/Info.plist;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSAppleEventsUsageDescription = "OpenCCman requires permission to execute AppleScript.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.1;
				PRODUCT_BUNDLE_IDENTIFIER = org.gewill.OpenCCman;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		BEA770DE2B2C904900AFA2F0 /* Build configuration list for PBXProject "OpenCCman" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BEA770F02B2C904A00AFA2F0 /* Debug */,
				BEA770F12B2C904A00AFA2F0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BEA770F22B2C904A00AFA2F0 /* Build configuration list for PBXNativeTarget "OpenCCman" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BEA770F32B2C904A00AFA2F0 /* Debug */,
				BEA770F42B2C904A00AFA2F0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		09103B682E40563800802E45 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gonzalezreal/swift-markdown-ui";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.1;
			};
		};
		09DBEC502E3BC4BC00DD9C5B /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sindresorhus/KeyboardShortcuts";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.3.0;
			};
		};
		09FB998E2E3D060700DBDB5D /* XCRemoteSwiftPackageReference "SwiftUIOverlayContainer" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/fatbobman/SwiftUIOverlayContainer";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.1;
			};
		};
		BE2C6D3C2B2D7EA700FC4112 /* XCRemoteSwiftPackageReference "swiftui-introspect" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/siteline/swiftui-introspect";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.1.1;
			};
		};
		BE2C6D652B2D817100FC4112 /* XCRemoteSwiftPackageReference "neumorphic" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gewill/neumorphic.git";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		BE3AEC8B2B2EF1DF00820673 /* XCRemoteSwiftPackageReference "SwiftUIRouter" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/frzi/SwiftUIRouter.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.3.2;
			};
		};
		BE3AEC9F2B2EF36500820673 /* XCRemoteSwiftPackageReference "BetterSafariView" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/stleamist/BetterSafariView";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
		BE3AECAC2B2EF4FA00820673 /* XCRemoteSwiftPackageReference "SwiftyUserDefaults" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sunshinejr/SwiftyUserDefaults";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.3.0;
			};
		};
		BE3F71E02B2F4F8F001496EA /* XCRemoteSwiftPackageReference "IQKeyboardManager" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/hackiftekhar/IQKeyboardManager";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.5.16;
			};
		};
		BE727E302B2DF65D00C63DB2 /* XCRemoteSwiftPackageReference "VisualEffects" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/twostraws/VisualEffects";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.0.3;
			};
		};
		BE781CB02CA131790022C4B8 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RevenueCat/purchases-ios-spm.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.3.4;
			};
		};
		BEA770F52B2C92B700AFA2F0 /* XCRemoteSwiftPackageReference "SwiftyOpenCC" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gewill/SwiftyOpenCC";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		BEA956372B31EB7B00E04625 /* XCRemoteSwiftPackageReference "ColorfulX" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Lakr233/ColorfulX";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		09103B692E40563800802E45 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 09103B682E40563800802E45 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */;
			productName = MarkdownUI;
		};
		09DBEC512E3BC4BC00DD9C5B /* KeyboardShortcuts */ = {
			isa = XCSwiftPackageProductDependency;
			package = 09DBEC502E3BC4BC00DD9C5B /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */;
			productName = KeyboardShortcuts;
		};
		09FB998F2E3D060700DBDB5D /* SwiftUIOverlayContainer */ = {
			isa = XCSwiftPackageProductDependency;
			package = 09FB998E2E3D060700DBDB5D /* XCRemoteSwiftPackageReference "SwiftUIOverlayContainer" */;
			productName = SwiftUIOverlayContainer;
		};
		BE2C6D3D2B2D7EA700FC4112 /* SwiftUIIntrospect */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE2C6D3C2B2D7EA700FC4112 /* XCRemoteSwiftPackageReference "swiftui-introspect" */;
			productName = SwiftUIIntrospect;
		};
		BE2C6D662B2D817100FC4112 /* Neumorphic */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE2C6D652B2D817100FC4112 /* XCRemoteSwiftPackageReference "neumorphic" */;
			productName = Neumorphic;
		};
		BE3AEC8C2B2EF1DF00820673 /* SwiftUIRouter */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE3AEC8B2B2EF1DF00820673 /* XCRemoteSwiftPackageReference "SwiftUIRouter" */;
			productName = SwiftUIRouter;
		};
		BE3AECA02B2EF36500820673 /* BetterSafariView */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE3AEC9F2B2EF36500820673 /* XCRemoteSwiftPackageReference "BetterSafariView" */;
			productName = BetterSafariView;
		};
		BE3AECAD2B2EF4FA00820673 /* SwiftyUserDefaults */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE3AECAC2B2EF4FA00820673 /* XCRemoteSwiftPackageReference "SwiftyUserDefaults" */;
			productName = SwiftyUserDefaults;
		};
		BE3F71E12B2F4F8F001496EA /* IQKeyboardManagerSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE3F71E02B2F4F8F001496EA /* XCRemoteSwiftPackageReference "IQKeyboardManager" */;
			productName = IQKeyboardManagerSwift;
		};
		BE727E312B2DF65D00C63DB2 /* VisualEffects */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE727E302B2DF65D00C63DB2 /* XCRemoteSwiftPackageReference "VisualEffects" */;
			productName = VisualEffects;
		};
		BE781CB12CA131790022C4B8 /* RevenueCat */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE781CB02CA131790022C4B8 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCat;
		};
		BE781CB32CA131790022C4B8 /* RevenueCatUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE781CB02CA131790022C4B8 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCatUI;
		};
		BEA770F62B2C92B700AFA2F0 /* OpenCC */ = {
			isa = XCSwiftPackageProductDependency;
			package = BEA770F52B2C92B700AFA2F0 /* XCRemoteSwiftPackageReference "SwiftyOpenCC" */;
			productName = OpenCC;
		};
		BEA956382B31EB7B00E04625 /* ColorfulX */ = {
			isa = XCSwiftPackageProductDependency;
			package = BEA956372B31EB7B00E04625 /* XCRemoteSwiftPackageReference "ColorfulX" */;
			productName = ColorfulX;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = BEA770DB2B2C904900AFA2F0 /* Project object */;
}
