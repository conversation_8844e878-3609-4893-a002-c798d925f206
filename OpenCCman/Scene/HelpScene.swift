import SwiftUI

struct HelpScene: View {
  @AppStorage(UserDefaultsKeys.selectedLocale.rawValue) var selectedLocale: LocaleConstants = .system
  @State private var helpContent: String = ""

  // MARK: - life cycle

  var body: some View {
    VStack(alignment: .center, spacing: 0) {
      navi
      list
    }
    .background(Color.Neumorphic.main)
    .onAppear {
      loadHelpContent()
    }
  }

  private func loadHelpContent() {
    helpContent = loadMarkdownFile(named: "Help", locale: selectedLocale)
  }

  private func loadMarkdownFile(named fileName: String, locale: LocaleConstants) -> String {
    let bundle = Bundle.main
    var resourceName = fileName

    // Try to load localized version first
    switch locale {
    case .zh_Hans:
      resourceName = "\(fileName)-zh-Hans"
    case .zh_Hant:
      resourceName = "\(fileName)-zh-Hant"
    default:
      resourceName = fileName
    }

    // Try localized version first, fallback to default
    if let path = bundle.path(forResource: resourceName, ofType: "md"),
       let content = try? String(contentsOfFile: path) {
      return content
    } else if let path = bundle.path(forResource: fileName, ofType: "md"),
              let content = try? String(contentsOfFile: path) {
      return content
    }

    return "Help content not found."
  }

  var navi: some View {
    ZStack(alignment: .center) {
      Text("Help")
        .font(.title)
        .foregroundColor(Color.Neumorphic.secondary)
      HStack {
        BackButton()
          .padding(.horizontal, Constant.padding * 2)
        Spacer()
      }
    }
    .padding(.vertical, Constant.padding)
    .foregroundColor(Color.Neumorphic.secondary)
    .background(Color.Neumorphic.main)
  }

  var list: some View {
    ZStack(alignment: .top) {
      Color.Neumorphic.main
        .ignoresSafeArea()
      VStack(alignment: .center, spacing: 10.0) {
        Link("Online help", destination: URL(string: selectedLocale.helpUrl)!)
          .padding(4)
          .overlay(
            RoundedRectangle(cornerRadius: 4)
              .stroke(Color.accent, lineWidth: 1)
          )
        Text("Convert Chinese text with [OpenCC](https://github.com/BYVoid/OpenCC)")

        ScrollView {
          VStack(alignment: .leading, spacing: 15.0) {
            if !helpContent.isEmpty {
              if #available(iOS 15.0, macOS 12.0, *) {
                // Use native markdown support
                Text(LocalizedStringKey(helpContent))
                  .textSelection(.enabled)
              } else {
                // Fallback for older versions - display as plain text
                Text(helpContent)
                  .font(.body)
              }
            } else {
              Text("Loading help content...")
                .font(.body)
                .foregroundColor(.secondary)
            }
          }
        }
      }
      .padding(Constant.padding)
      .foregroundColor(Color.Neumorphic.secondary)
      .ignoresSafeArea(edges: .bottom)
    }
  }
}

struct HelpView_Previews: PreviewProvider {
  static var previews: some View {
    HelpScene()
  }
}
