# OpenCCman Help

## Introduction

**Open Chinese Convert (OpenCC, 開放中文轉換)** is an opensource project for conversions between Traditional Chinese, Simplified Chinese and Japanese Kanji (Shinjitai). It supports character-level and phrase-level conversion, character variant conversion and regional idioms among Mainland China, Taiwan and Hong Kong. This is not translation tool between Mandarin and Cantonese, etc.

Convert Chinese text with [OpenCC](https://github.com/BYVoid/OpenCC)

## Features

Strictly differentiate between 「one simplified to many traditionals」 and 「one simplified to many variants」.

Completely compatible with different variants and can realize dynamic substitution.

Strictly scrutinize one-simplified-to-multiple-traditional entries, and the principle is 「if it can be divided, then it will be divided」.

Support Mainland China, Taiwan, Hong Kong, different variants and regional customary word conversion, such as 「裏」「裡」、「鼠標」「滑鼠」.

## Global Service (macOS)

OpenCCman provides multiple ways to convert Chinese text from any application:

### Method 1: Global Keyboard Shortcut (Recommended)

1. Go to **Settings > Global Shortcut** to configure your shortcut
2. Select any Chinese text in any application
3. Press your configured shortcut (default: `⌘⌥R`)
4. The text will be automatically converted and replaced
5. OpenCCman will open showing both original and converted text

### Method 2: Right-click Services

OpenCCman provides two right-click services:

• **"Convert Chinese Text with OpenCCman"** - Automatically converts and replaces the selected text
• **"Open Selected Text in OpenCCman"** - Opens the app with the selected text for manual conversion

**Steps:**
1. Select text in any app (Safari, TextEdit, etc.)
2. Right-click and choose your preferred service from the Services menu
3. For convert service: text is automatically converted and replaced
4. For open service: OpenCCman opens with the text ready for conversion

> **Tip:** You can also assign keyboard shortcuts to these services in **System Settings > Keyboard > Keyboard Shortcuts > Services > Text** for even faster access.

### Service Details

• **Convert Service:** Best for quick, automatic conversion when you're confident with current settings
• **Open Service:** Perfect when you want to review the text, adjust conversion settings, or keep both original and converted text visible
• **Both services** work in any macOS application that supports text selection

## Conclusion

The **keyboard shortcut method** is fastest for immediate conversion. Use the **"Open Selected Text" service** when you want to review or adjust settings before converting.

---

*For more information, visit the [online help](https://gewill.org/2023/12/17/introducing-OpenCCman-en/)*
